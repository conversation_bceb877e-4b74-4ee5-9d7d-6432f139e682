package com.bodhisearch;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.hamcrest.Matchers.hasSize;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.Test;
import org.keycloak.representations.idm.ClientRepresentation;
import org.keycloak.representations.idm.ClientScopeRepresentation;
import org.keycloak.representations.idm.GroupRepresentation;
import org.keycloak.representations.idm.RoleRepresentation;
import org.keycloak.representations.idm.UserRepresentation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Comprehensive test to verify the complete realm setup from
 * bodhi-realm-generated.json
 * This test validates that all realm entities are properly configured after
 * import.
 */
public class RealmSetupConfigTest extends BaseTest {
  private static final Logger LOGGER = LoggerFactory.getLogger(RealmSetupConfigTest.class);

  @Test
  public void testRealmBasicConfiguration() {
    LOGGER.info("Testing realm basic configuration");

    // Test realm existence and basic properties without full representation
    // to avoid version compatibility issues
    assertNotNull(realm, "Realm should exist");

    // Test basic realm functionality by checking exact counts
    List<ClientRepresentation> clients = realm.clients().findAll();
    assertNotNull(clients, "Should be able to access realm clients");
    // 8 clients total: 7 custom + 1 realm-management (built-in clients may vary)
    assertThat("Realm should have at least 8 clients", clients, hasSize(8));

    List<UserRepresentation> users = realm.users().list();
    assertNotNull(users, "Should be able to access realm users");
    // 12 users from configuration + admin user = 13 total
    assertThat("Realm should have exactly 13 users", users, hasSize(13));
  }

  @Test
  public void testClientScopesConfiguration() {
    LOGGER.info("Testing client scopes configuration");

    List<ClientScopeRepresentation> clientScopes = realm.clientScopes().findAll();
    // Total client scopes: 3 custom + 12 default = 15 total
    assertThat("Should have exactly 15 client scopes", clientScopes, hasSize(15));

    Map<String, ClientScopeRepresentation> scopeMap = clientScopes.stream()
        .collect(Collectors.toMap(ClientScopeRepresentation::getName, scope -> scope));

    // Verify custom client scopes exist (3 total)
    String[] expectedCustomScopes = { "roles", "scope_token_user", "scope_token_power_user" };
    for (String scopeName : expectedCustomScopes) {
      assertTrue(scopeMap.containsKey(scopeName), "Client scope '" + scopeName + "' should exist");
      ClientScopeRepresentation scope = scopeMap.get(scopeName);
      assertEquals("openid-connect", scope.getProtocol(), "Scope protocol should be openid-connect");
      assertEquals("true", scope.getAttributes().get("include.in.token.scope"),
          "Scope should be included in token scope");
    }
  }

  @Test
  public void testClientsConfiguration() {
    LOGGER.info("Testing clients configuration");

    List<ClientRepresentation> clients = realm.clients().findAll();
    Map<String, ClientRepresentation> clientMap = clients.stream()
        .collect(Collectors.toMap(ClientRepresentation::getClientId, client -> client));

    // Expected clients from the realm configuration (7 custom clients + built-ins)
    String[] expectedCustomClientIds = {
        "resource-abcd", "resource-wxyz", "resource-empty", "resource-make-first-admin",
        "resource-missing-group", "resource-missing-token-exchange-permission", "client-lmno"
    };

    // Verify we have at least our 7 custom clients + built-in clients
    assertTrue(clients.size() >= 8, "Should have at least 8 clients (7 custom + built-ins)");

    for (String clientId : expectedCustomClientIds) {
      assertTrue(clientMap.containsKey(clientId), "Client '" + clientId + "' should exist");
      ClientRepresentation client = clientMap.get(clientId);

      // Verify common client properties
      assertTrue(client.isEnabled(), "Client '" + clientId + "' should be enabled");
      assertEquals("client-secret", client.getClientAuthenticatorType(),
          "Client should use client-secret authentication");
      assertTrue(client.isStandardFlowEnabled(), "Standard flow should be enabled");
      assertFalse(client.isImplicitFlowEnabled(), "Implicit flow should be disabled");
      assertTrue(client.isDirectAccessGrantsEnabled(), "Direct access grants should be enabled");

      // Check client-specific properties based on configuration
      if ("client-lmno".equals(clientId)) {
        // client-lmno is configured as a public client with no service accounts
        assertTrue(client.isPublicClient(), "Client '" + clientId + "' should be public");
        assertFalse(client.isServiceAccountsEnabled(),
            "Service accounts should be disabled for client '" + clientId + "'");
      } else {
        // Other clients are confidential with service accounts enabled
        assertFalse(client.isPublicClient(), "Client '" + clientId + "' should not be public");
        assertTrue(client.isServiceAccountsEnabled(),
            "Service accounts should be enabled for client '" + clientId + "'");
      }

      assertEquals("openid-connect", client.getProtocol(), "Protocol should be openid-connect");
      assertFalse(client.isFullScopeAllowed(), "Full scope should not be allowed");
    }
  }

  @Test
  public void testUsersConfiguration() {
    LOGGER.info("Testing users configuration");

    // Expected users from the realm configuration (12 total)
    String[] expectedUsernames = {
        "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
        "<EMAIL>", "<EMAIL>", "<EMAIL>",
        "<EMAIL>", "<EMAIL>", "<EMAIL>",
        "<EMAIL>", "<EMAIL>",
        "<EMAIL>"
    };

    // Verify exact count: 12 configured users + 1 admin user = 13 total
    List<UserRepresentation> allUsers = realm.users().list();
    assertThat("Should have exactly 13 users total", allUsers, hasSize(13));

    for (String username : expectedUsernames) {
      // Use exact search to avoid partial matches
      List<UserRepresentation> users = realm.users().search(username, true);
      assertThat("User '" + username + "' should exist", users, hasSize(1));

      UserRepresentation user = users.get(0);
      assertEquals(username, user.getUsername(), "Username should match");
      assertEquals(username, user.getEmail(), "Email should match username");
      assertTrue(user.isEnabled(), "User should be enabled");
      assertTrue(user.isEmailVerified(), "User email should be verified");
      assertNotNull(user.getFirstName(), "User should have first name");
      assertNotNull(user.getLastName(), "User should have last name");
    }
  }

  @Test
  public void testGroupsConfiguration() {
    LOGGER.info("Testing groups configuration");

    List<GroupRepresentation> groups = realm.groups().groups();
    // Verify exact count: 5 top-level groups
    assertThat("Should have exactly 5 top-level groups", groups, hasSize(5));

    Map<String, GroupRepresentation> groupMap = groups.stream()
        .collect(Collectors.toMap(GroupRepresentation::getName, group -> group));

    // Expected top-level groups (5 total)
    String[] expectedGroupNames = {
        "users-resource-abcd", "users-resource-wxyz", "users-resource-empty",
        "users-resource-make-first-admin", "users-resource-missing-token-exchange-permission"
    };

    for (String groupName : expectedGroupNames) {
      assertTrue(groupMap.containsKey(groupName), "Group '" + groupName + "' should exist");
      GroupRepresentation group = groupMap.get(groupName);
      assertEquals("/" + groupName, group.getPath(), "Group path should be correct");

      // Get subgroups by querying the group resource directly
      List<GroupRepresentation> subGroups = realm.groups().group(group.getId()).getSubGroups(null, null, null);
      assertThat("Group '" + groupName + "' should have exactly 4 subgroups", subGroups, hasSize(4));

      Set<String> subGroupNames = subGroups.stream()
          .map(GroupRepresentation::getName)
          .collect(Collectors.toSet());
      assertThat("Subgroups should be correct", subGroupNames,
          containsInAnyOrder("users", "power-users", "managers", "admins"));
    }
  }

  @Test
  public void testClientRolesConfiguration() {
    LOGGER.info("Testing client roles configuration");

    // All resource clients that should have the 4 standard roles
    String[] resourceClientIds = {
        "resource-abcd", "resource-wxyz", "resource-empty",
        "resource-make-first-admin", "resource-missing-token-exchange-permission"
    };
    String[] expectedRoles = { "resource_user", "resource_power_user", "resource_manager", "resource_admin" };

    for (String clientId : resourceClientIds) {
      List<ClientRepresentation> clients = realm.clients().findByClientId(clientId);
      assertThat("Client '" + clientId + "' should exist", clients, hasSize(1));

      String clientUuid = clients.get(0).getId();
      List<RoleRepresentation> roles = realm.clients().get(clientUuid).roles().list();

      // Each resource client should have exactly 4 roles
      assertThat("Client '" + clientId + "' should have exactly 4 roles", roles, hasSize(4));

      Set<String> roleNames = roles.stream()
          .map(RoleRepresentation::getName)
          .collect(Collectors.toSet());

      for (String expectedRole : expectedRoles) {
        assertTrue(roleNames.contains(expectedRole),
            "Client '" + clientId + "' should have role '" + expectedRole + "'");
      }
    }
  }

  @Test
  public void testUserGroupMemberships() {
    LOGGER.info("Testing user group memberships");

    // Test specific user group assignments from the realm configuration
    verifyUserInGroup("<EMAIL>", "/users-resource-abcd/users");
    verifyUserInGroup("<EMAIL>", "/users-resource-abcd/power-users");
    verifyUserInGroup("<EMAIL>", "/users-resource-abcd/managers");
    verifyUserInGroup("<EMAIL>", "/users-resource-abcd/admins");
    verifyUserInGroup("<EMAIL>", "/users-resource-wxyz/users");
    verifyUserInGroup("<EMAIL>", "/users-resource-wxyz/managers");
    verifyUserInGroup("<EMAIL>", "/users-resource-wxyz/admins");

    // Verify users with no groups (5 users should have no group assignments)
    String[] usersWithNoGroups = {
        "<EMAIL>", "<EMAIL>", "<EMAIL>",
        "<EMAIL>", "<EMAIL>",
        "<EMAIL>"
    };

    for (String username : usersWithNoGroups) {
      List<UserRepresentation> users = realm.users().search(username, true);
      assertThat("User '" + username + "' should exist", users, hasSize(1));
      UserRepresentation user = users.get(0);
      List<GroupRepresentation> userGroups = realm.users().get(user.getId()).groups();
      assertThat("User '" + username + "' should have no groups", userGroups, hasSize(0));
    }
  }

  @Test
  public void testDefaultClientScopes() {
    LOGGER.info("Testing default client scopes configuration");
    List<ClientScopeRepresentation> allClientScopes = realm.clientScopes().findAll();
    assertThat("Should have exactly 15 client scopes", allClientScopes, hasSize(15));
    Set<String> allScopeNames = allClientScopes.stream()
        .map(ClientScopeRepresentation::getName)
        .collect(Collectors.toSet());

    // Verify expected optional client scopes exist (12 from
    // defaultOptionalClientScopes)
    String[] expectedOptionalScopes = {
        "offline_access", "address", "phone", "microprofile-jwt", "acr",
        "email", "profile", "role_list", "roles", "web-origins",
        "scope_token_user", "scope_token_power_user"
    };

    for (String expectedScope : expectedOptionalScopes) {
      assertTrue(allScopeNames.contains(expectedScope),
          "Client scope '" + expectedScope + "' should exist");
    }
  }

  @Test
  public void testAuthorizationConfiguration() {
    LOGGER.info("Testing authorization configuration");

    // Verify realm-management client exists and has authorization settings
    List<ClientRepresentation> realmMgmtClients = realm.clients().findByClientId("realm-management");
    assertThat("realm-management client should exist", realmMgmtClients, hasSize(1));

    ClientRepresentation realmMgmtClient = realmMgmtClients.get(0);
    // Check if authorization services are enabled via the authorizationServicesEnabled field
    Boolean authzEnabled = realmMgmtClient.getAuthorizationServicesEnabled();
    assertTrue(authzEnabled != null && authzEnabled,
        "realm-management should have authorization services enabled");

    // Note: Authorization settings (policies, resources, scopes) are complex nested
    // structures
    // that are difficult to verify through the admin API without deep inspection.
    // The fact that the realm imports successfully and authorization is enabled
    // indicates the authorization configuration is properly set up.
  }

  @Test
  public void testRealmImportSuccess() {
    LOGGER.info("Testing overall realm import success");
    // 1. Verify we can get a token for a predefined client
    String token = getTokenForClient(RESOURCE_ABCD, RESOURCE_SECRET);
    assertNotNull(token, "Should be able to get token for resource-abcd");

    // 2. Verify we can authenticate a user
    String userToken = getUserTokenWith(RESOURCE_ABCD, RESOURCE_SECRET, tokenUrl, "<EMAIL>", "pass");
    assertNotNull(userToken, "Should be able <NAME_EMAIL>");

    // 3. Verify realm is accessible via admin client with exact counts
    assertNotNull(realm, "Should be able to access realm via admin client");
    List<ClientRepresentation> clients = realm.clients().findAll();
    assertTrue(clients.size() >= 8, "Realm should have at least 8 clients");

    LOGGER.info("Realm setup verification completed successfully");
  }

  private void verifyUserInGroup(String username, String expectedGroupPath) {
    List<UserRepresentation> users = realm.users().search(username, true);
    assertThat("User '" + username + "' should exist", users, hasSize(1));

    UserRepresentation user = users.get(0);
    List<GroupRepresentation> userGroups = realm.users().get(user.getId()).groups();

    boolean foundGroup = userGroups.stream()
        .anyMatch(group -> expectedGroupPath.equals(group.getPath()));

    assertTrue(foundGroup, "User '" + username + "' should be in group '" + expectedGroupPath + "'");
  }
}
